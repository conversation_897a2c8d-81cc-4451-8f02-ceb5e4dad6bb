<!-- src/components/SupersetDashboard.vue -->
<template>
  <div class="superset-wrapper">
    <!-- 加载 / 错误占位 -->
    <div v-if="loading || error" class="placeholder">
      <span v-if="loading">Dashboard 加载中…</span>
      <span v-else-if="error">加载失败，请刷新重试</span>
    </div>

    <!-- Superset 会把 iframe 插到这个容器 -->
    <div
      v-show="!loading && !error"
      ref="mountPoint"
      class="dashboard-container"
    />
  </div>
</template>

<script>
// 如果已 npm 安装 sdk，可直接 import；否则在 public/index.html 用 <script> 引入
import { embedDashboard } from '@superset-ui/embedded-sdk'

export default {
  name: 'SupersetDashboard',
  props: {
    /* Superset 域名 */
    domain: {
      type: String,
      default: 'http://*************:8080'
    },
    /* 仪表盘 id */
    dashboardId: {
      type: String,
      required: true
    },
    /* 是否隐藏标题 */
    hideTitle: {
      type: Boolean,
      default: true
    },
    /* 过滤器是否默认展开 */
    filtersExpanded: {
      type: Boolean,
      default: true
    },
    /* 透传给 Superset 的 url 查询参数 */
    urlParams: {
      type: Object,
      default: () => ({})
    },
    /* 由父组件注入的 token 获取函数 */
    fetchGuestToken: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      loading: true,
      error: false
    }
  },
  mounted() {
    this.initDashboard()
  },
  methods: {
    /* 初始化仪表盘 */
    async initDashboard() {
      try {
        await embedDashboard({
          id: this.dashboardId,
          supersetDomain: this.domain.replace(/\/$/, ''),
          mountPoint: this.$refs.mountPoint,
          fetchGuestToken: this.fetchGuestToken, // 来自父组件
          dashboardUiConfig: {
            hideTitle: this.hideTitle,
            filters: { expanded: this.filtersExpanded },
            urlParams: this.urlParams
          },
          iframeSandboxExtras: [
            'allow-top-navigation',
            'allow-popups-to-escape-sandbox'
          ],
          debug: process.env.NODE_ENV === 'development'
        })
        this.loading = false
      } catch (e) {
        console.error('[SupersetDashboard] 初始化失败', e)
        this.loading = false
        this.error = true
      }
    }
  }
}
</script>

<style scoped>
.superset-wrapper {
  width: 100%;
  height: 100%;
}
.dashboard-container {
  width: 100%;
  height: 100vh;
}
.placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-family: Arial, sans-serif;
  color: #666;
  font-size: 16px;
}
</style>
